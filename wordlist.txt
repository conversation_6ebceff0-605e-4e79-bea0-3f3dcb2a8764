password
password123
admin
root
test
default
c2
c2server
teamserver
sliver
mythic
havoc
empire
cobalt
strike
metasploit
msf
payload
beacon
agent
implant
shell
backdoor
rat
trojan
malware
virus
worm
bot
botnet
zombie
drone
slave
master
controller
handler
listener
stager
dropper
loader
injector
exploit
shellcode
payload
reverse
bind
connect
back
shell
cmd
powershell
bash
sh
zsh
fish
tcsh
csh
ksh
dash
ash
perl
python
ruby
php
java
javascript
node
nodejs
npm
yarn
pnpm
bun
deno
rust
go
golang
c
cpp
csharp
dotnet
net
framework
core
standard
api
rest
soap
xml
json
yaml
toml
ini
cfg
conf
config
configuration
settings
options
parameters
args
arguments
flags
switches
commands
command
cmd
exec
execute
run
start
stop
pause
resume
restart
reload
refresh
update
upgrade
downgrade
install
uninstall
remove
delete
add
create
make
build
compile
link
assemble
disassemble
debug
trace
profile
benchmark
test
check
verify
validate
confirm
approve
accept
reject
deny
allow
permit
grant
revoke
ban
block
unblock
filter
search
find
locate
query
select
insert
update
delete
drop
create
alter
truncate
index
view
trigger
procedure
function
method
class
object
instance
variable
constant
parameter
argument
return
value
result
output
input
data
information
content
payload
body
header
footer
title
name
label
tag
id
identifier
key
token
secret
password
passphrase
pin
code
hash
checksum
digest
signature
certificate
license
key
public
private
encrypt
decrypt
encode
decode
compress
decompress
zip
unzip
tar
untar
gzip
gunzip
bzip2
bunzip2
xz
unxz
7z
rar
unrar
iso
img
dmg
vhd
vmdk
qcow2
vdi
vbox
vmware
hyperv
kvm
qemu
docker
container
image
layer
volume
network
bridge
host
nat
port
forward
tunnel
proxy
vpn
ssh
telnet
ftp
sftp
http
https
ssl
tls
tcp
udp
icmp
ip
ipv4
ipv6
mac
ether
wifi
wlan
lan
wan
internet
web
site
website
page
url
uri
link
href
anchor
button
form
input
output
textarea
select
option
checkbox
radio
submit
reset
cancel
ok
yes
no
true
false
on
off
enable
disable
active
inactive
visible
hidden
show
hide
open
close
start
stop
pause
play
record
save
load
import
export
print
scan
copy
cut
paste
undo
redo
select
all
none
clear
reset
refresh
reload
back
forward
home
end
up
down
left
right
next
previous
first
last
top
bottom
begin
finish
complete
done
ready
wait
busy
loading
processing
working
thinking
calculating
computing
analyzing
parsing
rendering
drawing
painting
writing
reading
listening
watching
monitoring
observing
tracking
following
chasing
hunting
searching
looking
finding
discovering
exploring
investigating
researching
studying
learning
teaching
training
educating
instructing
guiding
helping
assisting
supporting
serving
providing
offering
giving
taking
receiving
accepting
rejecting
refusing
denying
allowing
permitting
granting
revoking
banning
blocking
unblocking
filtering
searching
finding
locating
querying
selecting
inserting
updating
deleting
dropping
creating
altering
truncating
indexing
viewing
triggering
proceeding
functioning
methodizing
classing
objectifying
instancing
variabling
constanting
parametering
argumenting
returning
valuing
resulting
outputting
inputting
dataing
informing
contenting
payloading
bodying
headering
footering
titling
naming
labeling
tagging
identifying
keying
tokening
secreting
passwording
passphrasing
pinning
coding
hashing
checksuming
digesting
signing
certificating
licensing
keying
publicing
privating
encrypting
decrypting
encoding
decoding
compressing
decompressing
zipping
unzipping
tarring
untarring
gzipping
gunzipping
bzip2ing
bunzip2ing
xzing
unxzing
7zing
raring
unraring
isoing
imging
dmging
vhding
vmdking
qcow2ing
vdiing
vboxing
vmwaring
hyperving
kvming
qemuing
dockering
containering
imaging
layering
voluming
networking
bridging
hosting
natting
porting
forwarding
tunneling
proxying
vpning
sshing
telneting
ftping
sftping
httping
httpsing
ssling
tlsing
tcping
udping
icmping
iping
ipv4ing
ipv6ing
macing
ethering
wifiing
wlaning
laning
waning
interneting
webing
siting
websiting
paging
urling
uriing
linking
hrefing
anchoring
buttoning
forming
inputting
outputting
textareaing
selecting
optioning
checkboxing
radioing
submitting
resetting
canceling
oking
yesing
noing
trueing
falsing
oning
offing
enabling
disabling
activating
inactivating
visibilizing
hiding
showing
hiding
opening
closing
starting
stopping
pausing
playing
recording
saving
loading
importing
exporting
printing
scanning
copying
cutting
pasting
undoing
redoing
selecting
alling
noning
clearing
resetting
refreshing
reloading
backing
forwarding
homing
ending
upping
downing
lefting
righting
nexting
previousing
firsting
lasting
topping
bottoming
beginning
finishing
completing
doning
readying
waiting
busying
loading
processing
working
thinking
calculating
computing
analyzing
parsing
rendering
drawing
painting
writing
reading
listening
watching
monitoring
observing
tracking
following
chasing
hunting
searching
looking
finding
discovering
exploring
investigating
researching
studying
learning
teaching
training
educating
instructing
guiding
helping
assisting
supporting
serving
providing
offering
giving
taking
receiving
accepting
rejecting
refusing
denying
allowing
permitting
granting
revoking
banning
blocking
unblocking
